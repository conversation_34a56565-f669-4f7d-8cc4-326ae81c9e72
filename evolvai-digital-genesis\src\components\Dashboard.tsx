
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bot, 
  Code, 
  Settings, 
  BarChart3, 
  Users, 
  MessageSquare,
  Copy,
  CheckCircle,
  Eye,
  EyeOff,
  Save,
  LogOut
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const [embedCode, setEmbedCode] = useState('');
  const [savedEmbedCode, setSavedEmbedCode] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  // Load existing embed code from Supabase
  useEffect(() => {
    const loadEmbedCode = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('webchat_configs')
          .select('embed_code')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .single();

        if (error && error.code !== 'PGRST116') {
          console.error('Error loading embed code:', error);
          return;
        }

        if (data) {
          setSavedEmbedCode(data.embed_code);
        }
      } catch (error) {
        console.error('Error loading embed code:', error);
      }
    };

    loadEmbedCode();
  }, [user]);

  const saveEmbedCode = async () => {
    if (!embedCode.trim()) {
      toast({
        title: "Error",
        description: "Please enter embed code first",
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to save embed code",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      // First, deactivate any existing configs
      await supabase
        .from('webchat_configs')
        .update({ is_active: false })
        .eq('user_id', user.id);

      // Then insert the new config
      const { error } = await supabase
        .from('webchat_configs')
        .insert({
          user_id: user.id,
          embed_code: embedCode,
          is_active: true
        });

      if (error) {
        throw error;
      }

      setSavedEmbedCode(embedCode);
      toast({
        title: "Success",
        description: "Embed code saved! The webchat bot will now appear on your main page.",
      });
    } catch (error) {
      console.error('Error saving embed code:', error);
      toast({
        title: "Error",
        description: "Failed to save embed code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const clearEmbedCode = async () => {
    if (!user) return;

    setLoading(true);

    try {
      const { error } = await supabase
        .from('webchat_configs')
        .update({ is_active: false })
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      setSavedEmbedCode('');
      setEmbedCode('');
      toast({
        title: "Cleared",
        description: "Embed code removed. The webchat bot will no longer appear on your main page.",
      });
    } catch (error) {
      console.error('Error clearing embed code:', error);
      toast({
        title: "Error",
        description: "Failed to clear embed code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Code copied to clipboard",
    });
  };

  const stats = [
    { label: "Total Conversations", value: "1,247", icon: MessageSquare, color: "text-cyber-blue" },
    { label: "Active Bots", value: "8", icon: Bot, color: "text-purple" },
    { label: "Response Rate", value: "98.5%", icon: BarChart3, color: "text-pink" },
    { label: "Users Helped", value: "3,891", icon: Users, color: "text-green" },
  ];

  return (
    <div className="min-h-screen bg-cyber-dark">
      <div className="bg-gradient-to-r from-cyber-blue/10 via-purple/10 to-pink/10 border-b border-cyber-blue/20 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">
              EvolvAI Dashboard
            </h1>
            <p className="text-gray-400">Manage your webchat bot and monitor performance</p>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-gray-400">Welcome, {user?.email}</span>
            <Button
              onClick={handleSignOut}
              variant="outline"
              className="border-cyber-blue text-cyber-blue hover:bg-cyber-blue hover:text-white"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index} className="bg-cyber-darker border-cyber-blue/20 hover:border-cyber-blue/40 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm mb-1">{stat.label}</p>
                    <p className="text-2xl font-bold text-white">{stat.value}</p>
                  </div>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs defaultValue="embed" className="space-y-6">
          <TabsList className="bg-cyber-darker border border-cyber-blue/20">
            <TabsTrigger value="embed" className="data-[state=active]:bg-cyber-blue data-[state=active]:text-white">
              <Code className="w-4 h-4 mr-2" />
              Webchat Bot Embed
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-purple data-[state=active]:text-white">
              <BarChart3 className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-pink data-[state=active]:text-white">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="embed" className="space-y-6">
            <Card className="bg-cyber-darker border-cyber-blue/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Bot className="w-5 h-5 mr-2 text-cyber-blue" />
                  Webchat Bot Embed Code Manager
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Paste your webchat bot embed code here. It will automatically appear on your main website.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Embed Code
                  </label>
                  <Textarea
                    placeholder="Paste your webchat bot embed code here (HTML + CSS + JavaScript)..."
                    value={embedCode}
                    onChange={(e) => setEmbedCode(e.target.value)}
                    className="bg-cyber-dark border-cyber-blue/30 text-white font-mono text-sm"
                    rows={12}
                  />
                </div>

                <div className="flex gap-4">
                  <Button 
                    onClick={saveEmbedCode}
                    disabled={loading}
                    className="bg-gradient-to-r from-cyber-blue to-purple hover:from-cyber-blue-dark hover:to-purple/80 text-white"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {loading ? 'Saving...' : 'Save & Deploy Bot'}
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => setShowPreview(!showPreview)}
                    className="border-cyber-blue text-cyber-blue hover:bg-cyber-blue hover:text-white"
                  >
                    {showPreview ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
                    {showPreview ? 'Hide' : 'Show'} Preview
                  </Button>

                  {savedEmbedCode && (
                    <Button
                      variant="destructive"
                      onClick={clearEmbedCode}
                      disabled={loading}
                    >
                      {loading ? 'Clearing...' : 'Clear Bot'}
                    </Button>
                  )}
                </div>

                {savedEmbedCode && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="block text-sm font-medium text-gray-300">
                        Currently Active Embed Code
                      </label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(savedEmbedCode)}
                        className="border-cyber-blue text-cyber-blue hover:bg-cyber-blue hover:text-white"
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                    <Textarea
                      value={savedEmbedCode}
                      readOnly
                      className="bg-cyber-dark border-cyber-blue/30 text-white font-mono text-sm"
                      rows={8}
                    />
                    <div className="flex items-center text-green text-sm">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Webchat bot is active on your main page!
                    </div>
                  </div>
                )}

                {showPreview && embedCode && (
                  <div className="border border-cyber-blue/30 rounded-lg p-4 bg-cyber-dark">
                    <h3 className="text-white font-medium mb-3">Preview:</h3>
                    <div className="bg-gray-900 p-4 rounded border text-gray-300 font-mono text-xs">
                      <pre className="whitespace-pre-wrap">{embedCode}</pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card className="bg-cyber-darker border-cyber-blue/20">
              <CardHeader>
                <CardTitle className="text-white">Analytics Dashboard</CardTitle>
                <CardDescription className="text-gray-400">
                  Monitor your webchat bot performance and engagement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-64 text-gray-400">
                  <div className="text-center">
                    <BarChart3 className="w-16 h-16 mx-auto mb-4 text-cyber-blue" />
                    <p>Advanced analytics coming soon...</p>
                    <p className="text-sm mt-2">Real-time data visualization and insights</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card className="bg-cyber-darker border-cyber-blue/20">
              <CardHeader>
                <CardTitle className="text-white">Dashboard Settings</CardTitle>
                <CardDescription className="text-gray-400">
                  Configure your dashboard preferences and account settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center h-64 text-gray-400">
                  <div className="text-center">
                    <Settings className="w-16 h-16 mx-auto mb-4 text-purple" />
                    <p>Settings panel coming soon...</p>
                    <p className="text-sm mt-2">Customize your preferences</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
