
import React, { useState, useEffect } from 'react';
import { Menu, X } from 'lucide-react';
import { scrollToSection } from '@/lib/scrollToSection';

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { id: 'home', label: 'Home' },
    { id: 'services', label: 'Services' },
    { id: 'process', label: 'Process' },
    { id: 'reviews', label: 'Reviews' },
    { id: 'about', label: 'About' },
    { id: 'contact', label: 'Contact' },
  ];

  const handleMobileMenuToggle = () => {
    setIsMenuOpen(prev => {
      console.log('Menu toggled. New state:', !prev);
      return !prev;
    });
  };

  const handleNavClick = (sectionId: string) => {
    console.log('Nav item clicked:', sectionId);
    setIsMenuOpen(false);
    setTimeout(() => {
      scrollToSection(sectionId);
    }, 120);
  };

  return (
    <nav
      className={`fixed top-0 left-0 right-0 transition-all duration-500 ${isScrolled ? 'bg-cyber-dark/90 border-b border-cyber-blue/30 shadow-lg shadow-cyber-blue/5' : 'bg-transparent'} z-40`}
    >
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div
            className="flex items-center space-x-3 group cursor-pointer z-10 relative"
            onClick={() => handleNavClick('home')}
          >
            <div className="w-20 h-20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <img src="/lovable-uploads/064b3da1-41f1-4ea3-be56-c18f5a4eaad5.png" alt="EvolvAI Logo" className="w-20 h-20 object-contain" />
            </div>
          </div>
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center justify-center flex-1 z-10">
            <div className="flex items-center space-x-1">
              {navItems.map(item => (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.id)}
                  className="px-6 py-3 text-gray-300 hover:text-white transition-all duration-300 rounded-xl hover:bg-cyber-blue/10 relative group"
                >
                  {item.label}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-cyber-blue to-purple group-hover:w-8 transition-all duration-300"></div>
                </button>
              ))}
            </div>
          </div>
          <div className="hidden md:flex w-20"></div>
          {/* Mobile menu button */}
          <button
            onClick={handleMobileMenuToggle}
            className="md:hidden text-cyber-blue p-4 rounded-xl hover:bg-cyber-blue/10 active:bg-cyber-blue/20 transition-colors duration-300 z-20 relative min-h-[48px] min-w-[48px] touch-manipulation"
            aria-label="Toggle menu"
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
          >
            {isMenuOpen ? <X className="w-7 h-7" /> : <Menu className="w-7 h-7" />}
          </button>
        </div>
        {/* Mobile Navigation */}
        <div
          id="mobile-menu"
          className={`md:hidden transition-all duration-300 overflow-hidden ${isMenuOpen ? 'max-h-[500px] opacity-100 mt-6' : 'max-h-0 opacity-0'} absolute left-0 right-0 top-full z-50`}
          style={{
            pointerEvents: isMenuOpen ? 'auto' : 'none'
          }}
        >
          <div className="pb-6 border-t border-cyber-blue/20 pt-6 space-y-3 bg-cyber-dark rounded-xl shadow-lg">
            {navItems.map((item, index) => (
              <button
                key={item.id}
                onClick={(e) => handleNavClick(item.id, e)}
                className="block w-full text-left text-gray-300 hover:text-white active:text-cyber-blue transition-colors duration-300 py-4 px-6 rounded-xl hover:bg-cyber-blue/10 active:bg-cyber-blue/20 text-lg font-medium min-h-[48px] touch-manipulation"
                style={{
                  animationDelay: `${index * 50}ms`
                }}
              >
                {item.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;

