
import React, { useState, useEffect, useRef } from 'react';
import { Search, Code, Wrench } from 'lucide-react';

const processes = [{
  number: "01",
  title: "Analyze",
  description: "We start with a thorough analysis of your current workflows to see how AI could improve your processes.",
  icon: Search,
  details: ["Current workflow assessment", "Identify automation opportunities", "Performance metrics analysis", "ROI potential evaluation"]
}, {
  number: "02",
  title: "Build & Implement",
  description: "Then, our developers will start crafting custom AI solutions for your company, continuously prioritising quality and safety.",
  icon: Code,
  details: ["Custom AI development", "Integration with existing systems", "Rigorous testing protocols", "Security implementation"]
}, {
  number: "03",
  title: "Maintain & Improve",
  description: "After deployment, our team will keep working hard by providing support and continuously improving the implemented solutions.",
  icon: Wrench,
  details: ["24/7 monitoring and support", "Performance optimization", "Regular updates and improvements", "Scaling as your business grows"]
}];

const ProcessSection = () => {
  const [visibleSteps, setVisibleSteps] = useState<number[]>([]);
  const [activeStep, setActiveStep] = useState<number | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          processes.forEach((_, index) => {
            setTimeout(() => {
              setVisibleSteps(prev => [...prev, index]);
            }, index * 300);
          });
        }
      });
    }, {
      threshold: 0.1
    });
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    return () => observer.disconnect();
  }, []);
  
  return <section ref={sectionRef} className="py-32 px-4 bg-gradient-to-b from-slate-900 to-cyber-dark relative overflow-hidden">
      <div className="absolute inset-0 cyber-grid opacity-10"></div>
      
      {/* Background decorative elements */}
      <div className="absolute top-20 right-20 w-96 h-96 bg-purple/5 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-20 left-20 w-80 h-80 bg-cyber-blue/5 rounded-full blur-3xl animate-pulse" style={{
      animationDelay: '2s'
    }} />
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className={`text-center mb-20 transition-all duration-1000 ${visibleSteps.length > 0 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h2 className="text-6xl md:text-7xl font-bold mb-8 text-white">The Process</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our proven methodology ensures seamless AI integration that delivers measurable results
          </p>
        </div>

        <div className="space-y-8">
          {processes.map((process, index) => <div key={index} className={`transition-all duration-700 ${visibleSteps.includes(index) ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-12'}`} style={{
          transitionDelay: `${index * 200}ms`
        }}>
              <div className={`relative group cursor-pointer ${activeStep === index ? 'scale-[1.02]' : 'scale-100'} transition-all duration-500`} onMouseEnter={() => setActiveStep(index)} onMouseLeave={() => setActiveStep(null)}>
                {/* Main content card */}
                <div className="bg-gradient-to-r from-cyber-blue/5 via-purple/5 to-pink/5 border border-cyber-blue/20 rounded-3xl p-8 md:p-12 backdrop-blur-sm hover:border-cyber-blue/40 transition-all duration-500">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    
                    {/* Left side - Content */}
                    <div className={`${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                      <div className="flex items-center mb-6">
                        <span className="text-6xl font-bold text-cyber-blue mr-6">
                          {process.number}
                        </span>
                        <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br from-cyber-blue to-purple flex items-center justify-center`}>
                          <process.icon className="w-8 h-8 text-white" />
                        </div>
                      </div>
                      
                      <h3 className="text-4xl font-bold text-white mb-6">
                        {process.title}
                      </h3>
                      
                      <p className="text-lg text-gray-300 leading-relaxed mb-8">
                        {process.description}
                      </p>

                      {/* Process details */}
                      <div className="space-y-3">
                        {process.details.map((detail, detailIndex) => <div key={detailIndex} className="flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                            <div className="w-2 h-2 rounded-full bg-gradient-to-r from-cyber-blue to-purple mr-4"></div>
                            <span>{detail}</span>
                          </div>)}
                      </div>
                    </div>

                    {/* Right side - Visual element */}
                    <div className={`${index % 2 === 1 ? 'lg:order-1' : ''} relative`}>
                      <div className="bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-darker rounded-2xl p-8 border border-cyber-blue/20 shadow-lg">
                        {/* AI Analysis Dashboard for Analyze */}
                        {index === 0 && <div className="space-y-4">
                            <div className="flex justify-between items-center mb-6">
                              <h4 className="text-cyber-blue font-semibold text-lg">AI Workflow Analysis</h4>
                              <div className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                <span className="text-xs text-gray-400">Live Analysis</span>
                              </div>
                            </div>
                            
                            {/* Analysis metrics */}
                            <div className="grid grid-cols-2 gap-4 mb-6">
                              <div className="bg-cyber-blue/10 rounded-lg p-3 border border-cyber-blue/20">
                                <div className="text-xs text-gray-400 mb-1">Automation Potential</div>
                                <div className="text-xl font-bold text-cyber-blue">78%</div>
                              </div>
                              <div className="bg-purple/10 rounded-lg p-3 border border-purple/20">
                                <div className="text-xs text-gray-400 mb-1">Efficiency Gain</div>
                                <div className="text-xl font-bold text-purple">+42%</div>
                              </div>
                            </div>
                            
                            {/* Process flow visualization */}
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-400">Data Processing</span>
                                <span className="text-xs text-cyber-blue">Optimizable</span>
                              </div>
                              <div className="h-2 bg-cyber-blue/20 rounded overflow-hidden">
                                <div className="h-full bg-gradient-to-r from-cyber-blue to-purple w-3/4 rounded"></div>
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-400">Customer Service</span>
                                <span className="text-xs text-purple">High Priority</span>
                              </div>
                              <div className="h-2 bg-purple/20 rounded overflow-hidden">
                                <div className="h-full bg-gradient-to-r from-purple to-pink w-5/6 rounded"></div>
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-400">Report Generation</span>
                                <span className="text-xs text-pink">Ready for AI</span>
                              </div>
                              <div className="h-2 bg-pink/20 rounded overflow-hidden">
                                <div className="h-full bg-gradient-to-r from-pink to-cyber-blue w-4/5 rounded"></div>
                              </div>
                            </div>
                          </div>}
                        
                        {/* Code mockup for Build & Implement */}
                        {index === 1 && <div className="space-y-2">
                            <div className="flex space-x-2 mb-4">
                              <div className="w-3 h-3 rounded-full bg-red-500"></div>
                              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                              <div className="w-3 h-3 rounded-full bg-green-500"></div>
                            </div>
                            <div className="font-mono text-sm space-y-2">
                              <div className="text-cyber-blue">{'<html lang="en">'}</div>
                              <div className="text-gray-400 ml-4">{'<head>'}</div>
                              <div className="text-purple ml-8">{'<meta charset="UTF-8">'}</div>
                              <div className="text-gray-400 ml-4">{'</head>'}</div>
                              <div className="text-pink ml-4">{'<body>'}</div>
                              <div className="text-cyber-blue ml-8">{'<div class="ai-widget">'}</div>
                            </div>
                          </div>}

                        {/* Metrics for Maintain & Improve */}
                        {index === 2 && <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-cyber-blue">+38%</div>
                                <div className="text-xs text-gray-400">Efficiency</div>
                              </div>
                              <div className="text-center">
                                <div className="text-2xl font-bold text-purple">+25%</div>
                                <div className="text-xs text-gray-400">Speed</div>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-400">Operational cost</span>
                                <span className="text-pink">-11%</span>
                              </div>
                              <div className="h-2 bg-cyber-blue/20 rounded overflow-hidden">
                                <div className="h-full bg-gradient-to-r from-cyber-blue to-purple w-3/4 rounded"></div>
                              </div>
                            </div>
                          </div>}
                      </div>
                      
                      {/* Floating elements - reduced opacity for subtle effect */}
                      <div className="absolute -top-4 -right-4 w-8 h-8 bg-cyber-blue/2 rounded-full animate-ping"></div>
                      <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple/2 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>

                {/* Hover glow effect - removed pulse animation */}
                {activeStep === index && <div className="absolute -inset-2 bg-gradient-to-r from-cyber-blue via-purple to-pink rounded-3xl blur-xl opacity-[0.03]"></div>}
              </div>
            </div>)}
        </div>
      </div>
    </section>;
};

export default ProcessSection;
