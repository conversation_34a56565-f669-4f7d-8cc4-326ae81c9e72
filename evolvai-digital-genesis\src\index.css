
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 196 100% 50%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 196 100% 50%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-cyber-dark text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Disable problematic animations on mobile */
  @media (max-width: 768px) {
    .animate-float,
    .animate-pulse,
    .animate-glow,
    .animate-pulse-glow {
      animation: none !important;
    }

    /* Keep essential animations but reduce intensity */
    .animate-slide-up {
      animation-duration: 0.3s !important;
    }

    .animate-slide-left {
      animation-duration: 0.3s !important;
    }

    /* Disable hover effects that can interfere with touch */
    .hover\\:scale-105:hover {
      transform: none !important;
    }

    /* Ensure transforms don't interfere with touch */
    .group:hover .group-hover\\:scale-110 {
      transform: none !important;
    }
  }

  .text-shadow-glow {
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
  }

  .border-glow {
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
  }

  .bg-cyber-gradient {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  }

  .cyber-grid {
    background-image:
      linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }
  
  .glitch-text {
    position: relative;
  }
  
  .glitch-text::before,
  .glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  .glitch-text::before {
    animation: glitch 0.3s ease-in-out infinite alternate-reverse;
    color: #00d4ff;
    z-index: -1;
  }
  
  .glitch-text::after {
    animation: glitch 0.3s ease-in-out infinite alternate-reverse;
    color: #ec4899;
    z-index: -2;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #0a0a0f;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00d4ff, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #33e0ff, #a78bfa);
}

/* MOBILE-SAFE CHAT WIDGET - PREVENT TOUCH BLOCKING */
#botfusion-chat-iframe {
  z-index: 90 !important; /* Override embed code's z-index: 9999 for mobile compatibility */
}

/* CRITICAL: Prevent chat widget from blocking mobile interactions */
@media (max-width: 768px) {
  #botfusion-chat-iframe {
    pointer-events: none !important; /* Disable touch events on mobile */
  }

  /* Re-enable pointer events only when chat is actually being used */
  #botfusion-chat-iframe:hover,
  #botfusion-chat-iframe:focus,
  #botfusion-chat-iframe:active {
    pointer-events: auto !important;
  }

  /* Alternative: Only enable pointer events in the bottom-right corner where chat should be */
  #botfusion-chat-iframe {
    /* This will be overridden by JavaScript for better control */
  }
}
