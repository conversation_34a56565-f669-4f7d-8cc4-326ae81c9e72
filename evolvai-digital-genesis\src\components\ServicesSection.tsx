import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Zap, Mic, ArrowRight } from 'lucide-react';
import { scrollToSection } from '@/lib/scrollToSection';

const services = [
  {
    icon: Bot,
    title: "Chatbot Development",
    subtitle: "Intelligent conversational AI",
    description: "We develop intelligent chatbots that leverage advanced NLP to elevate customer interactions and streamline your business processes.",
    features: ["Natural Language Processing", "Multi-language Support", "Custom Training", "Real-time Analytics"],
    color: "cyber-blue",
    gradient: "from-cyber-blue/20 to-cyber-blue-dark/20",
    borderGradient: "from-cyber-blue to-cyber-blue-dark"
  },
  {
    icon: Zap,
    title: "Workflow Automations",
    subtitle: "Streamline operations",
    description: "We automate your workflows to streamline repetitive tasks, enhance efficiency, save time, and eliminate errors.",
    features: ["Process Optimization", "Smart Decision Making", "Integration APIs", "Custom Workflows"],
    color: "purple",
    gradient: "from-purple/20 to-violet-600/20",
    borderGradient: "from-cyber-purple to-violet-600"
  },
  {
    icon: Mic,
    title: "Voice Agent Development",
    subtitle: "Conversational AI assistants",
    description: "We create sophisticated voice agents that provide natural, human-like interactions for customer support, sales, and business automation.",
    features: ["Natural Speech Processing", "Real-time Conversations", "Multi-language Support", "Custom Voice Training"],
    color: "pink",
    gradient: "from-pink/20 to-rose-500/20",
    borderGradient: "from-cyber-pink to-rose-500"
  }
];

const ServicesSection = () => {
  const [visibleCards, setVisibleCards] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            services.forEach((_, index) => {
              setTimeout(() => {
                setVisibleCards(prev => [...prev, index]);
              }, index * 200);
            });
          }
        });
      },
      { threshold: 0.1 }
    );
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-32 px-4 relative overflow-hidden bg-gradient-to-b from-cyber-dark to-slate-900 z-0">
      <div
        className="absolute inset-0 cyber-grid opacity-10 pointer-events-none z-0"
      ></div>
      {/* Floating background elements */}
      <div
        className="absolute top-20 left-10 w-64 h-64 bg-cyber-blue/5 rounded-full blur-3xl animate-pulse pointer-events-none z-0"
      />
      <div
        className="absolute bottom-20 right-10 w-64 h-64 bg-purple/5 rounded-full blur-3xl animate-pulse pointer-events-none z-0"
        style={{ animationDelay: '1s' }}
      />
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="text-center mb-24">
          <div className={`transition-all duration-1000 ${visibleCards.length > 0 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <h2 className="text-6xl md:text-7xl font-bold mb-8 text-white">What We Do</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Cutting-edge AI solutions designed to transform your business operations and customer interactions
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-stretch">
          {services.map((service, index) => (
            <div
              key={index}
              className={`group relative transform transition-all duration-300 ease-out hover:scale-105 hover:-translate-y-2 ${visibleCards.includes(index) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-12'}`}
              style={{
                transitionDelay: `${index * 100}ms`
              }}
            >
              <div className={`relative p-[2px] rounded-3xl bg-gradient-to-br ${service.borderGradient} h-full`}>
                <div className="bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-darker rounded-3xl p-8 h-full shadow-lg flex flex-col">
                  {/* Service icon with animated background */}
                  <div className="relative mb-8">
                    <div className={`w-20 h-20 rounded-2xl bg-gradient-to-br ${service.borderGradient} flex items-center justify-center mb-6 relative overflow-hidden`}>
                      <service.icon className="w-10 h-10 text-white z-10" />
                      <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent pointer-events-none" />
                    </div>
                    <h3 className="text-3xl font-bold text-white mb-2">
                      {service.title}
                    </h3>
                    <p className="text-lg text-cyber-blue mb-4 font-medium">
                      {service.subtitle}
                    </p>
                    <p className="text-gray-300 leading-relaxed text-base">
                      {service.description}
                    </p>
                  </div>
                  <div className="space-y-4 mb-8 flex-grow">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-gray-400">
                        <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${service.borderGradient} mr-4`}></div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className={`text-center mt-20 transition-all duration-1000 delay-700 ${visibleCards.length >= 3 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div
            className="bg-gradient-to-r from-cyber-blue/10 via-purple/10 to-pink/10 border border-cyber-blue/20 rounded-3xl p-12 shadow-lg relative z-20"
          >
            <h3 className="text-4xl font-bold mb-6 text-white">
              Ready to transform your business?
            </h3>
            <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto">
              Let's discuss how our AI solutions can revolutionize your operations and drive unprecedented growth.
            </p>
            <div className="relative z-5">
              <button
                type="button"
                onClick={() => {
                  console.log('CTA: Get in touch clicked');
                  scrollToSection('contact');
                }}
                className="bg-gradient-to-r from-cyber-blue via-purple to-pink hover:from-cyber-blue-dark hover:via-purple/80 hover:to-pink/80 active:from-cyber-blue-dark active:via-purple/90 active:to-pink/90 text-white px-12 py-6 text-lg font-semibold rounded-2xl shadow-lg shadow-cyber-blue/20 hover:shadow-cyber-blue/40 transition-all duration-300 border-0 cursor-pointer flex items-center justify-center mx-auto min-h-[60px] relative touch-manipulation hover:scale-105 active:scale-95"
                tabIndex={0}
              >
                Get in touch
                <ArrowRight className="ml-2 w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
