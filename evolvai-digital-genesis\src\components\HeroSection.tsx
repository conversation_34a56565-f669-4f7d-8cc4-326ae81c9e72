
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Zap } from 'lucide-react';
import { scrollToSection } from '@/lib/scrollToSection';

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleGetStarted = () => {
    console.log('CTA: Get Started clicked');
    scrollToSection('contact');
  };
  const handleExplore = () => {
    console.log('CTA: Explore Services clicked');
    scrollToSection('services');
  };

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-cyber-dark via-slate-900 to-cyber-darker z-0">
      {/* Animated background elements */}
      <div className="absolute inset-0" style={{ zIndex: 1, pointerEvents: 'none' }}>
        <div className="absolute top-20 left-10 w-72 h-72 bg-cyber-blue/10 rounded-full blur-3xl animate-pulse pointer-events-none z-0" />
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple/10 rounded-full blur-3xl animate-pulse pointer-events-none z-0" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-cyber-blue/5 to-transparent rounded-full pointer-events-none z-0" />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden" style={{ zIndex: 2, pointerEvents: 'none' }}>
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-cyber-blue rounded-full animate-float pointer-events-none"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 6}s`,
              animationDuration: `${4 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 py-32 text-center relative z-10">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Main heading */}
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-black mb-8 leading-tight">
            <span className="text-white drop-shadow-2xl" style={{
              filter: 'drop-shadow(0 0 20px rgba(0, 212, 255, 0.5))',
              textShadow: '0 0 30px rgba(0, 212, 255, 0.6), 0 0 60px rgba(0, 212, 255, 0.4)'
            }}>
              Evolv
            </span>
            <span className="text-white drop-shadow-2xl" style={{
              textShadow: '0 0 20px rgba(255, 255, 255, 0.3)'
            }}>AI</span>
          </h1>

          {/* Subheading */}
          <div className="text-xl md:text-2xl lg:text-3xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
            <span className="text-cyber-blue font-semibold">
              Transforming businesses
            </span>
            <span className="text-gray-300"> with intelligent AI agents that </span>
            <span className="text-cyber-blue font-semibold">Evolve with you</span>
          </div>

          {/* Features grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 max-w-4xl mx-auto">
            {[
              { icon: Bot, text: "Smart AI Agents", gradient: "from-cyber-blue to-purple" },
              { icon: Zap, text: "Lightning Fast", gradient: "from-purple to-pink" },
              { icon: Sparkles, text: "Always Learning", gradient: "from-pink to-cyber-blue" }
            ].map((feature, index) => (
              <div
                key={index}
                className={`group p-6 bg-gradient-to-br from-white/5 to-white/10 shadow-lg border border-white/10 rounded-2xl hover:border-cyber-blue/50 transition-all duration-500 hover:scale-105 ${isVisible ? 'animate-slide-up' : 'opacity-0'}`}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                <div className={`w-12 h-12 bg-gradient-to-r ${feature.gradient} rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <p className="text-gray-300 font-medium">{feature.text}</p>
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center relative z-5">
            <button
              type="button"
              onClick={handleGetStarted}
              className="group bg-gradient-to-r from-cyber-blue to-purple hover:from-cyber-blue-dark hover:to-purple/80 active:from-cyber-blue-dark active:to-purple/90 text-white px-12 py-6 rounded-2xl font-semibold text-lg transition-all duration-300 shadow-lg shadow-cyber-blue/20 hover:shadow-cyber-blue/40 hover:scale-105 active:scale-95 flex items-center justify-center min-h-[60px] cursor-pointer relative touch-manipulation"
              tabIndex={0}
            >
              Get Started
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </button>

            <button
              type="button"
              onClick={handleExplore}
              className="group px-12 py-6 border-2 border-white/20 text-white hover:border-cyber-blue/50 hover:bg-cyber-blue/10 active:border-cyber-blue active:bg-cyber-blue/20 rounded-2xl font-semibold text-lg transition-all duration-300 backdrop-blur-sm hover:scale-105 active:scale-95 min-h-[60px] cursor-pointer relative touch-manipulation"
              tabIndex={0}
            >
              Explore Services
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;

