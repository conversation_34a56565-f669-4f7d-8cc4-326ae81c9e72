// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://oqjfxfitsofpabeyxuyg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9xamZ4Zml0c29mcGFiZXl4dXlnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5Mzg3NzAsImV4cCI6MjA2NTUxNDc3MH0.6lzD-3GhwGuZw01Nx0vIruv1a5JeQP3A6I1R6agkudk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);