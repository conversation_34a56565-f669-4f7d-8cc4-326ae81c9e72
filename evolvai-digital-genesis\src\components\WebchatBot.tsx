
import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

const WebchatBot = () => {
  const [embedCode, setEmbedCode] = useState('');
  
  useEffect(() => {
    const loadActiveEmbedCode = async () => {
      try {
        console.log('WebchatBot: Starting to load embed code...');
        
        // Check if Supabase is properly configured
        if (!supabase) {
          console.warn('WebchatBot: Supabase client not available, no embed code will be loaded');
          return;
        }

        // Try to get any active embed code from the database (public access)
        console.log('WebchatBot: Fetching active embed code from database...');
        const { data, error } = await supabase
          .from('webchat_configs')
          .select('embed_code')
          .eq('is_active', true)
          .maybeSingle();

        if (error) {
          console.warn('WebchatBot: Database query error:', error);
        } else if (data?.embed_code) {
          console.log('WebchatBot: Found embed code in database');
          setEmbedCode(data.embed_code);
          
          // Save to localStorage as backup
          try {
            localStorage.setItem('evolvai-embed-code', data.embed_code);
          } catch (storageError) {
            console.warn('WebchatBot: Failed to save to localStorage:', storageError);
          }
          return;
        } else {
          console.log('WebchatBot: No active embed code found in database');
        }

        // Enhanced localStorage fallback
        try {
          const savedEmbedCode = localStorage.getItem('evolvai-embed-code');
          if (savedEmbedCode && savedEmbedCode.trim()) {
            console.log('WebchatBot: Using embed code from localStorage');
            setEmbedCode(savedEmbedCode);
            return;
          }
        } catch (storageError) {
          console.warn('WebchatBot: localStorage access failed:', storageError);
        }

        // No fallback - user needs to configure their own embed code
        console.log('WebchatBot: No embed code configured. User needs to add one in the dashboard.');
        
      } catch (error) {
        console.error('WebchatBot: Error loading embed code:', error);
      }
    };

    loadActiveEmbedCode();

    // Set up real-time listener for changes to webchat_configs
    let subscription: any;
    
    const setupRealtimeListener = async () => {
      try {
        console.log('WebchatBot: Setting up real-time listener for embed code changes');
        
        subscription = supabase
          .channel('webchat_configs_changes')
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'webchat_configs'
            },
            (payload: any) => {
              console.log('WebchatBot: Real-time update received:', payload);
              
              if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
                if (payload.new && payload.new.is_active) {
                  console.log('WebchatBot: Updating embed code from real-time event');
                  setEmbedCode(payload.new.embed_code);
                  
                  // Update localStorage backup
                  try {
                    localStorage.setItem('evolvai-embed-code', payload.new.embed_code);
                  } catch (storageError) {
                    console.warn('WebchatBot: Failed to update localStorage:', storageError);
                  }
                }
              } else if (payload.eventType === 'DELETE' || (payload.new && !payload.new.is_active)) {
                console.log('WebchatBot: Embed code deactivated, clearing widget');
                setEmbedCode('');
                
                // Clear localStorage backup
                try {
                  localStorage.removeItem('evolvai-embed-code');
                } catch (storageError) {
                  console.warn('WebchatBot: Failed to clear localStorage:', storageError);
                }
              }
            }
          )
          .subscribe();
      } catch (error) {
        console.warn('WebchatBot: Failed to set up real-time listener:', error);
      }
    };

    setupRealtimeListener();

    // Cleanup function
    return () => {
      if (subscription) {
        console.log('WebchatBot: Cleaning up real-time subscription');
        supabase.removeChannel(subscription);
      }
    };
  }, []);

  useEffect(() => {
    if (embedCode) {
      console.log('WebchatBot: Injecting embed code...');
      
      try {
        // Create a temporary div to parse the embed code
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = embedCode;

        // Extract and inject styles
        const styles = tempDiv.querySelectorAll('style');
        styles.forEach(style => {
          const newStyle = document.createElement('style');
          newStyle.textContent = style.textContent;
          newStyle.setAttribute('data-webchat-style', 'true');
          document.head.appendChild(newStyle);
        });

        // Extract and inject scripts with proper error handling
        const scripts = tempDiv.querySelectorAll('script');
        scripts.forEach(script => {
          const newScript = document.createElement('script');
          if (script.src) {
            newScript.src = script.src;
            newScript.onerror = () => {
              console.error('WebchatBot: Failed to load script:', script.src);
            };
          } else {
            newScript.textContent = script.textContent;
          }
          // Copy all attributes with proper type checking
          Array.from(script.attributes).forEach(attr => {
            newScript.setAttribute(attr.name, attr.value);
          });
          newScript.setAttribute('data-webchat-script', 'true');
          document.body.appendChild(newScript);
        });

        // PRODUCTION-SAFE: Extract and inject other elements with strict mobile protection
        const otherElements = tempDiv.querySelectorAll(':not(style):not(script)');
        const resizeHandlers: (() => void)[] = []; // Store resize handlers for cleanup

        otherElements.forEach(element => {
          if (element.tagName && element instanceof Element) {
            const clonedElement = element.cloneNode(true) as Element;
            clonedElement.setAttribute('data-webchat-element', 'true');

            // CRITICAL: Prevent touch event blocking in production
            const elementStyle = clonedElement.style;
            elementStyle.pointerEvents = 'auto';

            // CRITICAL: DO NOT OVERRIDE Z-INDEX - let embed code handle it
            // The embed code's z-index: 9999 is necessary for proper mobile functionality

            // IMPORTANT: Only set position if not already set by embed code
            if (!elementStyle.position || elementStyle.position === 'static') {
              elementStyle.position = 'fixed';
            }

            // RESPECT EMBED CODE STYLING: Don't override if already set
            // The embed code has specific styling that should take precedence
            const hasEmbedStyling = clonedElement.id === 'botfusion-chat-iframe' ||
                                   clonedElement.querySelector('#botfusion-chat-iframe');

            if (!hasEmbedStyling) {
              // Only apply our custom styling if no embed-specific styling exists
              const isMobile = window.innerWidth <= 768;

              // Standard chat widget positioning - lower right
              elementStyle.bottom = elementStyle.bottom || '20px';
              elementStyle.right = elementStyle.right || '20px';
              elementStyle.top = 'auto'; // Remove any top positioning
              elementStyle.left = 'auto'; // Remove any left positioning

              if (isMobile) {
                // Mobile adjustments - still lower right but with MORE space for full widget
                elementStyle.bottom = '20px';
                elementStyle.right = '10px';
                elementStyle.maxHeight = 'calc(100vh - 80px)';
                elementStyle.maxWidth = 'calc(100vw - 20px)';
                elementStyle.minHeight = '400px';
                elementStyle.minWidth = '300px';
              } else {
                // Desktop positioning - standard chat widget location
                elementStyle.maxHeight = '600px';
                elementStyle.maxWidth = '400px';
                elementStyle.minHeight = '400px';
                elementStyle.minWidth = '300px';
              }
            }

            // PRODUCTION SAFETY: Add event listener to detect if it's blocking touch
            clonedElement.addEventListener('touchstart', (e) => {
              console.log('WebchatBot: Touch event detected on webchat element', e);
            });

            // RESPONSIVE: Add resize handler to adjust widget dimensions dynamically
            const handleResize = () => {
              const isMobileNow = window.innerWidth <= 768;
              const elementStyle = clonedElement.style;

              // RESPECT EMBED CODE STYLING: Don't override if embed-specific styling exists
              const hasEmbedStyling = clonedElement.id === 'botfusion-chat-iframe' ||
                                     clonedElement.querySelector('#botfusion-chat-iframe');

              if (!hasEmbedStyling) {
                // Only apply our custom responsive styling if no embed-specific styling exists
                if (isMobileNow) {
                  // Mobile adjustments - maintain all existing mobile fixes
                  elementStyle.bottom = '20px';
                  elementStyle.right = '10px';
                  elementStyle.maxHeight = 'calc(100vh - 80px)';
                  elementStyle.maxWidth = 'calc(100vw - 20px)';
                  elementStyle.minHeight = '400px';
                  elementStyle.minWidth = '300px';
                } else {
                  // Desktop adjustments
                  elementStyle.bottom = '20px';
                  elementStyle.right = '20px';
                  elementStyle.maxHeight = '600px';
                  elementStyle.maxWidth = '400px';
                  elementStyle.minHeight = '400px';
                  elementStyle.minWidth = '300px';
                }
              }
              // If embed styling exists, let the embed code's CSS handle responsiveness
            };

            window.addEventListener('resize', handleResize);
            resizeHandlers.push(handleResize); // Store for cleanup

            document.body.appendChild(clonedElement);

            // INDUSTRY STANDARD SOLUTION: Create clickable overlay for chat bubble
            setTimeout(() => {
              const botfusionIframe = document.getElementById('botfusion-chat-iframe');
              if (botfusionIframe) {
                console.log('WebchatBot: Implementing industry standard chat widget solution');

                // The iframe is already set to pointer-events: none via CSS
                // Create a clickable area that forwards clicks to the iframe
                const clickableArea = document.createElement('div');
                clickableArea.className = 'chat-widget-clickable-area';
                clickableArea.setAttribute('data-chat-clickable', 'true');

                // Simply enable the iframe for direct interaction
                const enableIframeInteraction = (event) => {
                  console.log('WebchatBot: Enabling iframe for direct interaction');
                  event.preventDefault();
                  event.stopPropagation();

                  // Enable pointer events on iframe permanently
                  botfusionIframe.style.pointerEvents = 'auto';

                  // Remove the overlay since iframe is now clickable
                  clickableArea.style.display = 'none';

                  // Show a brief message to user
                  const message = document.createElement('div');
                  message.style.position = 'fixed';
                  message.style.bottom = '100px';
                  message.style.right = '20px';
                  message.style.backgroundColor = 'rgba(0, 212, 255, 0.9)';
                  message.style.color = 'white';
                  message.style.padding = '8px 12px';
                  message.style.borderRadius = '6px';
                  message.style.fontSize = '12px';
                  message.style.zIndex = '10001';
                  message.innerHTML = 'Chat enabled! Tap the chat bubble to open.';
                  message.setAttribute('data-chat-message', 'true');

                  document.body.appendChild(message);

                  // Remove message after 3 seconds
                  setTimeout(() => {
                    message.remove();
                  }, 3000);

                  console.log('WebchatBot: Iframe enabled for direct clicking');
                };

                clickableArea.addEventListener('click', enableIframeInteraction);
                clickableArea.addEventListener('touchstart', enableIframeInteraction);

                document.body.appendChild(clickableArea);

                console.log('WebchatBot: Chat widget clickable area created');
              }
            }, 100);
            console.log('WebchatBot: Injected element with mobile-safe positioning', {
              tagName: clonedElement.tagName,
              zIndex: elementStyle.zIndex,
              position: elementStyle.position,
              isMobile
            });
          }
        });

        console.log('WebchatBot: Embed code injected successfully');

        // Cleanup function
        return () => {
          console.log('WebchatBot: Cleaning up injected elements...');

          // Remove all resize event listeners
          resizeHandlers.forEach(handler => {
            window.removeEventListener('resize', handler);
          });

          // Remove injected styles
          const injectedStyles = document.querySelectorAll('style[data-webchat-style]');
          injectedStyles.forEach(style => style.remove());

          // Remove injected scripts
          const injectedScripts = document.querySelectorAll('script[data-webchat-script]');
          injectedScripts.forEach(script => script.remove());

          // Remove injected elements
          const injectedElements = document.querySelectorAll('[data-webchat-element]');
          injectedElements.forEach(element => element.remove());

          // Remove common widget elements by ID
          const possibleIds = ['botfusion-chat-iframe', 'evolvai-chat-widget', 'webchat-widget'];
          possibleIds.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
              element.remove();
            }
          });

          // Remove chat clickable areas and messages
          const chatClickables = document.querySelectorAll('[data-chat-clickable]');
          chatClickables.forEach(clickable => clickable.remove());

          const chatMessages = document.querySelectorAll('[data-chat-message]');
          chatMessages.forEach(message => message.remove());
        };
      } catch (error) {
        console.error('WebchatBot: Error injecting embed code:', error);
      }
    }
  }, [embedCode]);

  return null; // This component doesn't render anything visible
};

export default WebchatBot;
