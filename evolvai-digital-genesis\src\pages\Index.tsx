
import React from 'react';
import Navigation from '@/components/Navigation';
import HeroSection from '@/components/HeroSection';
import ServicesSection from '@/components/ServicesSection';
import ProcessSection from '@/components/ProcessSection';
import ReviewsSection from '@/components/ReviewsSection';
import WebchatBot from '@/components/WebchatBot';

const Index = () => {
  return (
    <div className="bg-cyber-dark text-white touch-manipulation">
      <Navigation />
      
      <main className="relative">
        <div id="home">
          <HeroSection />
        </div>
        
        <div id="services">
          <ServicesSection />
        </div>
        
        <div id="process">
          <ProcessSection />
        </div>
        
        <div id="reviews">
          <ReviewsSection />
        </div>
        
        <div id="about" className="py-32 px-4 bg-gradient-to-b from-slate-900 to-cyber-dark relative overflow-hidden">
          <div className="absolute inset-0 cyber-grid opacity-10 pointer-events-none"></div>
          <div className="max-w-4xl mx-auto text-center relative z-20">
            <h2 className="text-6xl font-bold mb-12 text-white">
              About EvolvAI
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed mb-12 max-w-3xl mx-auto">
              We're pioneering the future of AI-powered business solutions. Our team of experts 
              combines cutting-edge technology with deep industry knowledge to create AI agents 
              that don't just respond—they evolve, learn, and adapt to your unique business needs.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
              {[{
                number: "2+",
                label: "Years Experience"
              }, {
                number: "98.9%",
                label: "Uptime"
              }, {
                number: "30+",
                label: "Business Solutions Deployed"
              }].map((stat, index) => (
                <div key={index} className="bg-gradient-to-br from-cyber-blue/5 to-purple/5 border border-cyber-blue/20 rounded-2xl p-8 backdrop-blur-sm hover:border-cyber-blue/40 transition-all duration-300">
                  <div className="text-5xl font-bold text-cyber-blue mb-3">
                    {stat.number}
                  </div>
                  <div className="text-gray-400">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div id="contact" className="py-32 px-4 bg-gradient-to-b from-cyber-dark to-cyber-darker relative overflow-hidden">
          <div className="absolute inset-0 cyber-grid opacity-10 pointer-events-none"></div>
          <div className="max-w-4xl mx-auto text-center relative z-20">
            <h2 className="text-6xl font-bold mb-12 text-white">
              Ready to Evolve?
            </h2>
            <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">Let's discuss how EvolvAI can transform your business with cutting-edge AI solutions. Our 24/7 Assistant in the lower right can answer questions, send you a email and you can also hit the microphone button and talk!</p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              
              
            </div>
          </div>
        </div>
        
        <footer className="border-t border-cyber-blue/20 py-16 px-4 bg-cyber-darker relative z-20">
          <div className="max-w-7xl mx-auto text-center">
            <div className="mb-4">
              <img src="/lovable-uploads/064b3da1-41f1-4ea3-be56-c18f5a4eaad5.png" alt="EvolvAI Logo" className="h-12 mx-auto" />
            </div>
            <p className="text-gray-400 mb-4">Evolving AI. Evolving Business. Evolving Future.</p>
            <div className="text-sm text-gray-500">
              © 2024 EvolvAI. All rights reserved.
            </div>
          </div>
        </footer>
      </main>

      <WebchatBot />
    </div>
  );
};

export default Index;
