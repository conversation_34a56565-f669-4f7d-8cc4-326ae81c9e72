import React, { useState, useEffect, useRef } from 'react';
import { Star } from 'lucide-react';
const reviews = [{
  quote: "EvolvAI's solutions save us a ton of money on a monthly basis. Highly recommend working with them!",
  author: "<PERSON>",
  position: "CTO - Wave",
  rating: 5,
  avatar: "DW"
}, {
  quote: "EvolvAI has significantly enhanced our efficiency, resulting in the completion of more work every day.",
  author: "<PERSON>",
  position: "CCO - Kama Inc.",
  rating: 5,
  avatar: "JM"
}, {
  quote: "EvolvAI is a game-changer for any business looking to leverage AI effectively.",
  author: "<PERSON>",
  position: "CEO - <PERSON>ra Innovations",
  rating: 5,
  avatar: "OJ"
}];
const ReviewsSection = () => {
  const [visibleReviews, setVisibleReviews] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          reviews.forEach((_, index) => {
            setTimeout(() => {
              setVisibleReviews(prev => [...prev, index]);
            }, index * 150);
          });
        }
      });
    }, {
      threshold: 0.1
    });
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    return () => observer.disconnect();
  }, []);
  return <section ref={sectionRef} className="py-32 px-4 bg-gradient-to-b from-cyber-dark via-slate-900 to-cyber-dark relative overflow-hidden">
      <div className="absolute inset-0 cyber-grid opacity-10"></div>
      
      {/* Background decorative elements */}
      <div className="absolute top-20 left-20 w-64 h-64 bg-cyber-blue/5 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple/5 rounded-full blur-3xl animate-pulse" style={{
      animationDelay: '1s'
    }} />
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className={`text-center mb-20 transition-all duration-1000 ${visibleReviews.length > 0 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h2 className="text-6xl md:text-7xl font-bold mb-8 text-white">
            Reviews
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            What our clients say about working with EvolvAI
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review, index) => <div key={index} className={`transition-all duration-700 ${visibleReviews.includes(index) ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`} style={{
          transitionDelay: `${index * 100}ms`
        }}>
              <div className="bg-gradient-to-br from-cyber-blue/5 via-purple/5 to-pink/5 border border-cyber-blue/20 rounded-3xl p-8 shadow-lg hover:border-cyber-blue/40 transition-all duration-500 hover:scale-105 h-full flex flex-col">
                
                {/* Rating stars */}
                <div className="flex mb-6">
                  {[...Array(review.rating)].map((_, i) => <Star key={i} className="w-5 h-5 fill-cyber-blue text-cyber-blue mr-1" />)}
                </div>
                
                {/* Review quote */}
                <blockquote className="text-gray-300 text-lg leading-relaxed mb-8 flex-grow">
                  "{review.quote}"
                </blockquote>
                
                {/* Author info */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-cyber-blue to-purple rounded-full flex items-center justify-center text-white font-semibold mr-4">
                    {review.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-white">{review.author}</div>
                    
                  </div>
                </div>
              </div>
            </div>)}
        </div>

        {/* Trust indicators */}
        <div className={`text-center mt-20 transition-all duration-1000 delay-700 ${visibleReviews.length >= 3 ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          
        </div>
      </div>
    </section>;
};
export default ReviewsSection;